<template>
  <div class="staff-location">
    <!-- 顶部导航栏 -->
    <van-nav-bar title="人员定位" left-arrow fixed @click-left="onClickLeft" />

    <!-- 位置选项卡 -->
    <div class="location-tabs">
      <div class="tab-item">位置信息</div>
      <div class="tab-buttons">
        <van-button
          size="small"
          type="primary"
          plain
          class="tab-button location-btn"
          @click="locationReport"
          >定位回传</van-button
        >
        <van-button
          size="small"
          type="primary"
          class="tab-button refresh-btn"
          @click="getCurrentLocation"
          >刷新定位</van-button
        >
      </div>
    </div>

    <!-- 地图区域 -->
    <div class="map-container">
      <div class="map-placeholder" @click="showMapDialog = true">
        <img
          src="@/assets/common/map-placeholder.png"
          alt="地图"
          class="map-image"
        />
        <div class="map-center-marker"></div>
        <div class="map-location-tip">点击查看位置</div>
      </div>

      <!-- 位置信息展示 -->
      <div class="location-info">
        <div class="location-address">当前位置: {{ location.address }}</div>
        <div class="location-coordinates">
          经纬度: {{ location.longitude.toFixed(6) }},
          {{ location.latitude.toFixed(6) }}
        </div>
      </div>
    </div>

    <!-- 搜索框 -->
    <div class="search-container">
      <div class="search-box">
        <div class="filter-dropdown" @click="showFilterPopup = true">
          <span>{{ selectedFilter || "全部" }}</span>
          <van-icon name="arrow-down" size="12" />
        </div>
        <van-search
          v-model="searchText"
          placeholder="搜索"
          shape="round"
          background="transparent"
          @search="onSearch"
        />
      </div>
    </div>

    <!-- 筛选弹出层 -->
    <van-popup v-model="showFilterPopup" position="bottom" round>
      <van-picker
        show-toolbar
        title="选择状态"
        :columns="filterOptions"
        @confirm="onFilterConfirm"
        @cancel="showFilterPopup = false"
      />
    </van-popup>

    <!-- 地图弹窗 -->
    <van-popup
      v-model="showMapDialog"
      position="bottom"
      :style="{ height: '80%', width: '100%' }"
    >
      <div class="map-dialog">
        <Map
          v-if="showMapDialog"
          ref="mapComponent"
          :coordinates="coordinates"
        />
        <div class="map-dialog-footer">
          <van-button type="primary" block @click="confirmLocation"
            >确认位置</van-button
          >
        </div>
      </div>
    </van-popup>

    <!-- 人员列表 -->
    <div class="staff-list">
      <div
        class="staff-item"
        v-for="(staff, index) in filteredStaffList"
        :key="staff.id || index"
        @click="viewStaffDetail(staff)"
      >
        <div class="staff-title">
          <div class="title-content">
            <div class="staff-avatar">
              <van-icon name="manager" color="#1989fa" size="20" />
            </div>
            <span>{{ staff.userName }}</span>
          </div>
          <span
            class="status-tag"
            :class="staff.status == 1 ? 'status-online' : 'status-offline'"
            >{{ staff.status == 1 ? "在线" : "离线" }}</span
          >
        </div>
        <div class="staff-info">
          <div class="info-row">
            <span class="label">当前位置：</span>
            <div class="value">{{ staff.address || "暂无位置信息" }}</div>
          </div>
          <div class="info-row">
            <span class="label">部门名称：</span>
            <div class="value">{{ staff.deptName || "暂无部门信息" }}</div>
          </div>
          <div class="info-row">
            <span class="label">角色名称：</span>
            <div class="value">{{ staff.roleName || "暂无角色信息" }}</div>
          </div>
          <div class="info-row">
            <span class="label">联系方式：</span>
            <div class="value">{{ staff.mobile || "暂无联系方式" }}</div>
          </div>
        </div>
      </div>

      <!-- 空数据状态 -->
      <div
        class="empty-state"
        v-if="filteredStaffList.length === 0 && !loading"
      >
        <van-empty description="暂无人员数据" />
      </div>

      <!-- 加载状态 -->
      <div class="loading-state" v-if="loading">
        <van-loading type="spinner" color="#1989fa" />
        <span>加载中...</span>
      </div>
    </div>
  </div>
</template>

<script>
import { signOrOut } from "@/api/clockIn";
import { groupByUserList, personPosition } from "@/api/staffLocation";
import Map from "@/components/map/index.vue";
export default {
  name: "StaffLocation",
  data() {
    return {
      loading: false,
      filterType: "全部",
      filterOptions: ["全部", "在线", "离线"],
      searchText: "",
      location: {
        longitude: 120.123456,
        latitude: 30.123456,
        address: "获取定位中...",
      },
      statusMap: {
        全部: "",
        在线: "1",
        离线: "0",
      },
      staffList: [],
      showFilterPopup: false,
      showMapDialog: false,
      selectedFilter: "全部",
      coordinates: [], //点位经纬度
    };
  },
  components: {
    Map,
  },
  mounted() {
    this.getCurrentLocation();
    this.fetchStaffList();
  },
  methods: {
    onClickLeft() {
      this.$router.go(-1);
    },

    // 获取当前位置
    getCurrentLocation() {
      if (this.isApp) {
        this.$toast.loading({
          message: "获取位置中...",
          duration: 0,
        });

        window.parent.postMessage(
          {
            cmd: "getPosition",
            options: {
              type: "gcj02",
            },
          },
          "*"
        );
        const watchPosition = (event) => {
          if (event.data.cmd == "getPosition") {
            if (event.data.value && event.data.value.longitude) {
              let res = event.data.value;
              this.location.longitude = res.longitude;
              this.location.latitude = res.latitude;
              this.coordinates = [
                this.location.longitude,
                this.location.latitude,
              ];
              // 调用高德地图API将经纬度转换为地址
              this.getAddressByCoords(
                this.location.longitude,
                this.location.latitude
              );
              console.log("获取位置成功:", this.location);
              this.$toast.clear();
              setTimeout(() => {
                window.removeEventListener("message", watchPosition);
              }, 2000);
            }
          }
        };
        window.addEventListener("message", watchPosition);
      } else {
        if (navigator.geolocation) {
          this.$toast.loading({
            message: "获取位置中...",
            duration: 0,
          });
          navigator.geolocation.getCurrentPosition(
            (position) => {
              this.location.longitude = position.coords.longitude;
              this.location.latitude = position.coords.latitude;
              this.coordinates = [
                this.location.longitude,
                this.location.latitude,
              ];
              // 调用高德地图API将经纬度转换为地址
              this.getAddressByCoords(
                this.location.longitude,
                this.location.latitude
              );
              console.log("获取位置成功:", this.location);
              this.$toast.clear();
            },
            (error) => {
              console.error("获取位置失败:", error);
              this.$toast.clear();
              this.$toast.fail("获取位置失败，请确保已开启定位权限");
            },
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 0,
            }
          );
        } else {
          this.$toast.fail("您的浏览器不支持获取地理位置");
        }
      }
    },

    // 根据经纬度获取地址信息（使用高德地图API）
    getAddressByCoords(lon, lat) {
      this.$toast.loading({
        message: "获取地址信息...",
        duration: 0,
      });

      // 创建高德地图API脚本，如果已存在则直接使用
      if (window.AMap) {
        this.convertCoordsToAddress(lon, lat);
        return;
      }

      // 如果没有加载过高德地图API，则先加载
      const script = document.createElement("script");
      script.type = "text/javascript";
      script.src =
        "https://webapi.amap.com/maps?v=2.0&key=b7a3da85edcdeecd9ae06c1a80f150c0&security=1";

      // 添加安全密钥配置
      window._AMapSecurityConfig = {
        securityJsCode: "758307c13c7a89718b29b7322c7b1bee",
      };

      script.onload = () => {
        this.convertCoordsToAddress(lon, lat);
      };
      script.onerror = () => {
        this.$toast.clear();
        this.$toast.fail("地图加载失败");
        this.location.address = `位置坐标: (${lon.toFixed(6)}, ${lat.toFixed(
          6
        )})`;
      };
      document.head.appendChild(script);
    },

    // 使用高德地图API进行坐标转换和地址解析
    convertCoordsToAddress(lon, lat) {
      // 使用高德地图的地理编码功能
      window.AMap.plugin("AMap.Geocoder", () => {
        const geocoder = new window.AMap.Geocoder();
        const lnglat = [lon, lat];

        geocoder.getAddress(lnglat, (status, result) => {
          this.$toast.clear();
          console.log(status, result, "status");
          if (status === "complete" && result.info === "OK") {
            // 解析成功，获取地址
            if (result.regeocode && result.regeocode.formattedAddress) {
              this.location.address = result.regeocode.formattedAddress;
              console.log("地址解析成功:", this.location.address);
            } else {
              this.location.address = `位置坐标: (${lon.toFixed(
                6
              )}, ${lat.toFixed(6)})`;
            }
          } else {
            // 解析失败，使用默认格式
            console.error("地址解析失败:", result);
            this.location.address = `位置坐标: (${lon.toFixed(
              6
            )}, ${lat.toFixed(6)})`;
            this.$toast.fail("地址解析失败");
          }
        });
      });
    },

    // 定位回传接口
    async locationReport() {
      if (!this.location.longitude || !this.location.latitude) {
        this.$toast("位置信息获取失败，请稍后重试");
        this.getCurrentLocation();
        return;
      }

      this.$dialog
        .confirm({
          title: "确认上报",
          message: "确定要上报当前位置吗？",
          confirmButtonText: "确认上报",
          cancelButtonText: "取消",
        })
        .then(async () => {
          try {
            this.$toast.loading({
              message: "上报中...",
              forbidClick: true,
              duration: 0,
            });

            const reportData = {
              lat: this.location.latitude.toString(),
              lon: this.location.longitude.toString(),
              address: this.location.address,
            };

            console.log("上报数据:", reportData);

            const res = await personPosition(reportData);

            if (res.code === 200) {
              this.$toast.success("上报成功");
              // 刷新人员列表
              this.fetchStaffList();
            } else {
              this.$toast.fail(res.msg || "上报失败");
            }
          } catch (error) {
            console.error("上报异常:", error);
            this.$toast.fail("上报失败，请稍后重试");
          } finally {
            this.$toast.clear();
          }
        })
        .catch(() => {
          // 用户取消
        });
    },

    // 获取人员列表数据
    async fetchStaffList() {
      try {
        this.loading = true;
        const params = {
          status: this.statusMap[this.selectedFilter] || "",
          // 可以添加其他查询参数
          userName: this.searchText || "",
        };

        const res = await groupByUserList(params);

        if (res.code === 200 && res.data) {
          this.staffList = res.data || [];
        } else {
          this.$toast.fail(res.msg || "获取人员列表失败");
          this.staffList = [];
        }
      } catch (error) {
        console.error("获取人员列表异常:", error);
        this.$toast.fail("获取人员列表失败，请稍后重试");
        this.staffList = [];
      } finally {
        this.loading = false;
      }
    },

    onFilterConfirm(value) {
      this.selectedFilter = value;
      this.showFilterPopup = false;
      this.fetchStaffList();
    },

    onSearch() {
      this.fetchStaffList();
    },

    confirmLocation() {
      // 这里可以从地图组件获取选择的位置
      if (
        this.$refs.mapComponent &&
        this.$refs.mapComponent.getSelectedLocation
      ) {
        const selectedLocation = this.$refs.mapComponent.getSelectedLocation();
        if (selectedLocation) {
          this.location.longitude = selectedLocation.longitude;
          this.location.latitude = selectedLocation.latitude;
          this.location.address =
            selectedLocation.address || this.location.address;
        }
      }
      this.showMapDialog = false;
    },

    viewStaffDetail(staff) {
      // 跳转到人员详情页面，可以传递用户ID等信息
      this.$router.push({
        path: "/staffLocationDetail",
        query: {
          id: staff.id,
          userId: staff.userId,
        },
      });
    },
  },
  computed: {
    // 根据筛选条件过滤人员列表
    filteredStaffList() {
      return this.staffList;
    },
  },
};
</script>

<style lang="scss" scoped>
.staff-location {
  min-height: 100vh;
  background-color: #f7f8fa;
  padding-top: 46px;
}

.location-tabs {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 10px 15px;
  background-color: #fff;
  border-bottom: 1px solid #f5f6f8;
  .tab-item {
    font-size: 15px;
    font-weight: 500;
    color: #333;
  }

  .tab-buttons {
    display: flex;
    gap: 10px;

    .tab-button {
      font-size: 12px;
      height: 30px;
      padding: 0 12px;
      border-radius: 4px;
    }

    .location-btn {
      border: unset;
      color: #409eff;
      background: #e3eeff;
    }

    .refresh-btn {
      background-color: #428ffc;
      color: white;
      border: unset;
    }
  }
}

.map-container {
  position: relative;
  margin: 0 0 10px 0;

  .map-placeholder {
    position: relative;
    height: 200px;
    display: flex;
    justify-content: center;
    align-items: center;
    background-color: #fff;
    overflow: hidden;

    .map-image {
      width: 100%;
      height: 100%;
      object-fit: cover;
      border-radius: 28px;
      padding: 16px 16px 0 16px;
    }

    .map-center-marker {
      position: absolute;
      width: 18px;
      height: 18px;
      background-color: #1989fa;
      border: 2px solid #fff;
      border-radius: 50%;
      top: 50%;
      left: 50%;
      transform: translate(-50%, -50%);
      z-index: 1;
      box-shadow: 0 2px 4px rgba(0, 0, 0, 0.2);
    }

    .map-location-tip {
      position: absolute;
      bottom: 50px;
      left: 50%;
      transform: translateX(-50%);
      background-color: rgba(0, 0, 0, 0.6);
      color: white;
      padding: 5px 12px;
      border-radius: 16px;
      font-size: 12px;
    }
  }

  .location-info {
    padding: 12px 15px;
    background-color: #fff;

    .location-address {
      font-size: 14px;
      color: #333;
      margin-bottom: 6px;
      line-height: 1.4;
      overflow: hidden;
      text-overflow: ellipsis;
      display: -webkit-box;
      -webkit-line-clamp: 2;
      -webkit-box-orient: vertical;
    }

    .location-coordinates {
      font-size: 13px;
      color: #666;
    }
  }
}

.search-container {
  display: flex;
  align-items: center;
  padding: 0 15px;
  background-color: #fff;

  .search-box {
    flex: 1;
    display: flex;
    align-items: center;

    .filter-dropdown {
      flex: 0 0 80px;
      height: 32px;
      display: flex;
      justify-content: center;
      align-items: center;
      background-color: #f5f5f5;
      border-radius: 16px;
      padding: 0 12px;
      margin-right: 8px;
      margin-left: 3px;

      span {
        font-size: 13px;
        color: #333;
        margin-right: 3px;
      }

      .van-icon {
        margin-top: 1px;
      }
    }

    .van-search {
      flex: 1;
      padding: 8px 0;
    }
  }
}

.staff-list {
  margin-top: 10px;
  background-color: #fff;
  min-height: 200px;
  position: relative;

  .staff-item {
    display: flex;
    flex-direction: column;
    padding: 15px;
    background-color: #fff;
    margin-bottom: 8px;
    border-bottom: 1px solid #f2f2f2;

    &:last-child {
      border-bottom: none;
    }

    .staff-title {
      font-size: 16px;
      font-weight: 500;
      color: #333;
      margin-bottom: 12px;
      display: flex;
      justify-content: space-between;
      align-items: center;

      .title-content {
        display: flex;
        align-items: center;
        flex: 1;

        .staff-avatar {
          width: 36px;
          height: 36px;
          border-radius: 50%;
          background-color: #f0f9ff;
          display: flex;
          justify-content: center;
          align-items: center;
          margin-right: 10px;
        }

        span {
          font-size: 15px;
          color: #333;
          font-weight: 500;
        }
      }

      .status-tag {
        font-size: 12px;
        padding: 2px 8px;
        border-radius: 4px;
        font-weight: normal;

        &.status-online {
          color: #07c160;
          background-color: rgba(7, 193, 96, 0.1);
        }

        &.status-offline {
          color: #969799;
          background-color: rgba(150, 151, 153, 0.1);
        }
      }
    }

    .staff-info {
      .info-row {
        display: flex;
        margin-bottom: 8px;
        font-size: 13px;
        color: #333;
        line-height: 1.5;

        &:last-child {
          margin-bottom: 0;
        }

        .label {
          color: #999;
          flex: 0 0 70px;
        }

        .value {
          color: #333;
          word-break: break-all;
          flex: 1;
        }
      }
    }
  }

  .empty-state {
    padding: 40px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
  }

  .loading-state {
    padding: 20px 0;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;

    span {
      margin-top: 8px;
      font-size: 14px;
      color: #969799;
    }
  }
}

// 新增地图弹窗样式
.map-dialog {
  height: 100%;
  display: flex;
  flex-direction: column;

  &-footer {
    padding: 10px;
    background: #fff;
    box-shadow: 0 -2px 10px rgba(0, 0, 0, 0.1);
  }
}

// 调整地图容器高度
.MapContainer {
  height: 0; // 隐藏底部固定地图
}
</style>
