<template>
  <div class="layout-container">
    <div class="content-area">
      <router-view />
    </div>
    <van-tabbar v-model="active" active-color="#1989fa" inactive-color="#7d7e80" style="z-index: 9000;">
      <van-tabbar-item icon="home-o" to="/">首页</van-tabbar-item>
      <van-tabbar-item icon="setting-o" to="/personCenter">设置</van-tabbar-item>
    </van-tabbar>
  </div>
</template>

<script>

export default {
  name: 'LayoutIndex',
  components: {},
  props: {},
  data() {
    return {
      active: 0
    }
  },
  computed: {

  },
  watch: {
    '$route'(val) {
      const name = val.name
      if (name == 'Home' && this.active != 0) {
        this.active = 0
      } else if (name == 'PersonCenter' && this.active != 1) {
        this.active = 1
      }
    }
  },
  mounted() {

  },
  created() {},
  methods: {}
}
</script>

<style lang="scss" scoped>
.layout-container {
  height: 100vh;
  display: flex;
  flex-direction: column;
  position: relative;
}

.content-area {
  flex: 1;
  overflow-y: auto;
  padding-bottom: 80px; /* 为底部tab栏留出更多空间，确保内容不被遮挡 */
  box-sizing: border-box;
}

/* 确保tabbar固定在底部 */
:deep(.van-tabbar) {
  position: fixed !important;
  bottom: 0 !important;
  left: 0 !important;
  right: 0 !important;
}
</style>
