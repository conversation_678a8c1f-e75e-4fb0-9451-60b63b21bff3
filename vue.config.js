"use strict";
const path = require("path");
// const defaultSettings = require('./src/settings.js')

function resolve(dir) {
  return path.join(__dirname, dir);
}

// 应用标题
const name = "金华综合执法";

// const port = process.env.port || process.env.npm_config_port || 80 // 端口
// vue.config.js 配置说明
//官方vue.config.js 参考文档 https://cli.vuejs.org/zh/config/#css-loaderoptions
// 这里只列一部分，具体配置参考文档
let filePath = './static/js/'
let Timestamp = new Date().getTime()
module.exports = {
  // 部署生产环境和开发环境下的URL。
  // 默认情况下，Vue CLI 会假设你的应用是被部署在一个域名的根路径上
  // 例如 https://www.ruoyi.vip/。如果应用被部署在一个子路径上，你就需要用这个选项指定这个子路径。例如，如果你的应用被部署在 https://www.ruoyi.vip/admin/，则设置 baseUrl 为 /admin/。
  publicPath: process.env.NODE_ENV === "production" ? "/ygfMobileJcApp" : "/",
  // publicPath: process.env.NODE_ENV === "production" ? "/ygfMobileJc" : "/", //isApp为true时打包app。false打包H5
  // 在npm run build 或 yarn build 时 ，生成文件的目录名称（要和baseUrl的生产环境路径一致）（默认dist）
  outputDir: "build",
  // 用于放置生成的静态资源 (js、css、img、fonts) 的；（项目打包之后，静态资源会放在这个文件夹下）
  assetsDir: "static",
  // 是否开启eslint保存检测，有效值：ture | false | 'error'
  lintOnSave: process.env.NODE_ENV === "development",
  // 如果你不需要生产环境的 source map，可以将其设置为 false 以加速生产环境构建。
  productionSourceMap: false,
  // webpack-dev-server 相关配置
  devServer: {
    host: "0.0.0.0",
    port: 8080,
    open: true,
    allowedHosts: ["localhost", ".localhost", "127.0.0.1", "all"],
    // 开发环境下显示详细日志
    logLevel: process.env.NODE_ENV === 'development' ? 'debug' : 'info',
    // 热重载配置
    hot: true,
    // 代理配置
    proxy: {
      [process.env.VUE_APP_BASE_API]: {
        target: "http://************",
        // 备用服务器地址（注释保留以备切换）
        // target: "http://*************:9000", // 盛铭
        // target: "http://*************:9000",
        // target: "http://************:9000",
        // target: "http://************:9000",
        changeOrigin: true,
        secure: false, // 如果是https接口，需要配置这个参数
        pathRewrite: {
          ["^" + process.env.VUE_APP_BASE_API]: process.env.VUE_APP_BASE_API,
        },
        // 代理超时设置
        timeout: 30000,
      },
    },
  },
  transpileDependencies: ["vant"],
  css: {
    loaderOptions: {
      scss: {
        // 如果需要配置 scss，可以在这里添加
        // additionalData: `@import "@/styles/variables.scss";`
      },
    },
  },
  configureWebpack: {
    name: name,
    resolve: {
      alias: {
        "@": resolve("src"),
        "@assets": resolve("src/assets"),
        "@components": resolve("src/components"),
        "@views": resolve("src/views"),
        "@api": resolve("src/api"),
        "@utils": resolve("src/util"),
      },
    },

    // 生产环境下的优化配置
    ...(process.env.NODE_ENV === 'production' ? {
      // webpack 配置 解决js缓存的问题
      output: {
        // 输出重构  打包编译后的 文件目录 文件名称 【模块名称.时间戳】
        filename: `${filePath}[name]-${Timestamp}.js`,
        chunkFilename: `${filePath}[name]-${Timestamp}.js`,
      },
      // 代码分割优化
      optimization: {
        splitChunks: {
          chunks: 'all',
          cacheGroups: {
            vendor: {
              name: 'chunk-vendors',
              test: /[\\/]node_modules[\\/]/,
              priority: 10,
              chunks: 'initial'
            },
            vant: {
              name: 'chunk-vant',
              test: /[\\/]node_modules[\\/]vant[\\/]/,
              priority: 20,
            },
            common: {
              name: 'chunk-common',
              minChunks: 2,
              priority: 5,
              reuseExistingChunk: true
            }
          }
        }
      }
    } : {})
  },
  chainWebpack: (config) => {
    // 配置 htmlWebpackPlugin 的 title
    config
      .plugin('html')
      .tap(args => {
        args[0].title = name;
        return args;
      });

    config.module
      .rule("svg")
      .exclude.add(resolve("src/assets/icons/svg"))
      .end();

    config.module
      .rule("icons")
      .test(/\.svg$/)
      .include.add(resolve("src/assets/icons/svg"))
      .end()
      .use("svg-sprite-loader")
      .loader("svg-sprite-loader")
      .options({
        symbolId: "icon-[name]",
      });
  },
};
