<template>
  <div class="container">
    <!-- 头部大图 -->
    <div class="banner">
      <img src="@/assets/home/<USER>" alt="防控治安秩序通告" />
    </div>

    <!-- 功能模块区域 -->
    <div class="modules-container">
      <div class="module-grid">
        <div
          class="module-item"
          v-for="(item, index) in currentMenuList"
          :key="index"
          @click="goToModule(item)"
        >
          <div class="module-icon">
            <img :src="item.icon" alt="" />
          </div>
          <div class="module-name">{{ item.name }}</div>
        </div>
      </div>
    </div>

    <!-- 底部卡片区域 -->
    <div class="card-container">
      <div class="card-row">
        <div class="card-item" @click="goToModule2(cardList[0])">
          <div class="card-content">
            <div class="card-icon">
              <img :src="cardList[0].icon" alt="" />
            </div>
            <div class="card-info">
              <div class="card-title">{{ cardList[0].name }}</div>
              <div class="card-desc">{{ cardList[0].desc }}</div>
            </div>
          </div>
        </div>
        <div class="card-item" @click="goToModule2(cardList[1])">
          <div class="card-content">
            <div class="card-icon">
              <img :src="cardList[1].icon" alt="" />
            </div>
            <div class="card-info">
              <div class="card-title">{{ cardList[1].name }}</div>
              <div class="card-desc">{{ cardList[1].desc }}</div>
            </div>
          </div>
        </div>
      </div>
      <div class="card-row">
        <div class="card-item" @click="goToModule2(cardList[2])">
          <div class="card-content">
            <div class="card-icon">
              <img :src="cardList[2].icon" alt="" />
            </div>
            <div class="card-info">
              <div class="card-title">{{ cardList[2].name }}</div>
              <div class="card-desc">{{ cardList[2].desc }}</div>
            </div>
          </div>
        </div>
        <div class="card-item position-item" @click="goToModule2(cardList[3])">
          <div class="card-content">
            <div class="card-icon">
              <img :src="cardList[3].icon" alt="" />
            </div>
            <div class="card-info">
              <div class="card-title">{{ cardList[3].name }}</div>
              <div class="card-desc">{{ cardList[3].desc }}</div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script>
import {getAPPList, getUrl} from "@/api/common";
import { getToken } from "@/util/auth";

export default {
  name: "Home",
  components: {},
  data() {
    return {
      currentMenuList: [],
      moduleList: [
        {
          name: "犬类管理",
          icon: require("@/assets/home/<USER>"),
          path: process.env.VUE_APP_BASE_ygf_zzd,
          key: 'qlgl'
        },
        {
          name: "监督指挥",
          icon: require("@/assets/home/<USER>"),
          path: process.env.VUE_APP_BASE_jiandumobile,
          key: 'jdzh'
        },
        {
          name: "站前管理",
          icon: require("@/assets/home/<USER>"),
          path: process.env.VUE_APP_BASE_zhanqian,
          key: 'zqgl'
        },
        {
          name: "违停查处",
          icon: require("@/assets/home/<USER>"),
          path: "/",
          key: 'wtcc'
        },
        {
          name: "考核任务",
          icon: require("@/assets/home/<USER>"),
          path: process.env.VUE_APP_BASE_assessment,
          key: 'khrw'
        },
        {
          name: "行政执法综合评价",
          icon: require("@/assets/home/<USER>"),
          path: "",
          key: 'xzzfzhpj'
        },
        {
          name: "三色预警",
          icon: require("@/assets/home/<USER>"),
          path: "",
          key: 'ssyj'
        },
        {
          name: "三书一函",
          icon: require("@/assets/home/<USER>"),
          path: "",
          key: 'ssyh'
        },
      ],
      cardList: [
        {
          name: "案卷档案",
          desc: "案件记录存档",
          icon: require("@/assets/home/<USER>"),
          path: "/CaseFileArchives",
        },
        {
          name: "已办任务",
          desc: "个人已办任务",
          icon: require("@/assets/home/<USER>"),
          path: "/completedTasks",
        },
        {
          name: "考勤打卡",
          desc: "签到",
          icon: require("@/assets/home/<USER>"),
          path: "/clockIn",
        },
        {
          name: "人员定位",
          desc: "定位人员位置",
          icon: require("@/assets/home/<USER>"),
          path: "/staffLocation",
        },
      ],
    };
  },
  methods: {
    getList() {
      getAPPList({name:"浙政钉通用app权限"}).then(res => {
        this.currentMenuList = this.moduleList.filter(item => {
          return res.data.indexOf(item.key) > -1;
        });
      })
    },
    async goToModule(item) {
      if (item.name == "行政执法综合评价") {
        getUrl({type: "zheding", module: "xzzfpjzb"}).then((res) => {
          if (res.code == 200) {
            window.location.href = res.data.url;
          }
        });
      } else if (item.name == "三色预警") {
        getUrl({type: "zheding", module: "ssyj"}).then((res) => {
          if (res.code == 200) {
            window.location.href = res.data.url;
          }
        });
      } else if (item.name == "三书一函") {
        getUrl({type: "zheding", module: "ssyh"}).then(async (res) => {
          if (res.code == 200) {
            window.location.href = res.data.url;
          }
        });
      } else {
        if (item.path) {
          let token = ""
          const tokenResult = await getToken()
          console.log(tokenResult, "-----------token获取结果-----------")

          if (tokenResult && tokenResult['YGF-MOBILE-Token']) {
            token = tokenResult['YGF-MOBILE-Token']
          }

          if (item.name == '犬类管理') {
            window.location.href = item.path += `/?token=${token}#/`;
          } else {
            window.location.href = item.path;
          }
        }
      }
    },
    goToModule2(item) {
      if (item.path) {
        this.$router.push(item.path);
      }
    },
  },
  mounted() {
    this.getList()
  }
};
</script>

<style lang="scss" scoped>
.container {
  background: #f5f5f5;
  padding-bottom: 20px;
  padding-top: 20px;
  /* 移除固定高度限制，让内容自然展开 */
}

.banner {
  width: 100%;
  padding: 0 16px;
  margin-top: 10px;

  img {
    width: 100%;
    border-radius: 8px;
    height: auto;
  }
}

.modules-container {
  background: #ffffff;
  border-radius: 8px;
  margin: 16px;
  padding: 20px 0;

  .module-grid {
    display: grid;
    grid-template-columns: repeat(4, 1fr);
    grid-gap: 15px 0;

    .module-item {
      display: flex;
      flex-direction: column;
      align-items: center;

      .module-icon {
        width: 40px;
        height: 40px;
        margin-bottom: 8px;

        img {
          width: 100%;
          height: 100%;
        }
      }

      .module-name {
        width: 58px;
        font-size: 12px;
        color: #333;
        text-align: center;
      }
    }
  }
}

.card-container {
  padding: 0 16px;

  .card-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 16px;

    .card-item {
      width: 48%;
      background: #ffffff;
      border-radius: 8px;
      position: relative;

      .card-content {
        display: flex;
        padding: 16px;

        .card-icon {
          width: 35px;
          height: 35px;
          margin-right: 12px;

          img {
            width: 100%;
            height: 100%;
          }
        }

        .card-info {
          .card-title {
            font-size: 14px;
            font-weight: 500;
            color: #333;
            margin-bottom: 5px;
          }

          .card-desc {
            font-size: 12px;
            color: #999;
          }
        }
      }
    }
  }
}
</style>
