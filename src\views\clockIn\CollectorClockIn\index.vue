<!--考勤打卡页面-->
<template>
  <div class="clock-in-page">
    <!-- 导航栏 -->
    <van-nav-bar title="考勤打卡" left-arrow fixed @click-left="onClickLeft" />

    <div class="head-bg"></div>

    <!-- 用户信息卡片 -->
    <div class="user-card">
      <div class="user-info">
        <div class="avatar">
          <van-image round width="40" height="40" :src="avatar" />
        </div>
        <div class="info">
          <div class="name">{{ user?.nickName || "未知用户" }}</div>
          <div class="date">
            {{ currentDate }} {{ currentTime }}
            <span class="dept-name">{{ user?.deptName || "" }}</span>
          </div>
        </div>
      </div>
      <div class="card-right">
        <van-image round width="30" height="30" :src="dateIcon" />
      </div>
    </div>

    <!-- 打卡状态 - 时间轴样式 -->
    <div class="clock-status">
      <div v-if="loadingInfo" class="loading-container">
        <van-loading color="#1989fa" />
        <div class="loading-text">加载打卡信息...</div>
      </div>
      <div v-else-if="loadError" class="error-container">
        <div class="error-text">打卡信息加载失败</div>
        <van-button plain type="info" size="small" @click="retryLoading"
          >重新加载</van-button
        >
      </div>
      <template v-else>
        <div class="timeline">
          <!-- 时间轴线 -->
          <div class="timeline-line" v-if="clockInStatus !== 'none'"></div>

          <!-- 上班打卡节点 -->
          <div class="timeline-item">
            <div
              class="timeline-dot"
              :class="{ active: clockInStatus !== 'none' }"
            ></div>
            <div class="timeline-content">
              <div class="timeline-title">上班时间 {{ workStartTime }}</div>

              <template v-if="clockInStatus !== 'none'">
                <div class="timeline-info">
                  <div class="time-label">
                    打卡时间 {{ clockInTime }}
                    <span class="status-tag" :class="{ 'late-tag': isLate }">
                      {{ isLate ? "迟到" : "已打卡" }}
                    </span>
                  </div>
                  <div
                    class="location-image"
                    v-if="signFilePath && signFilePath.length > 0"
                  >
                    <van-swipe
                      :autoplay="3000"
                      indicator-color="#1989fa"
                      :show-indicators="signFilePath.length > 1"
                      @change="onSignSwipeChange"
                    >
                      <van-swipe-item
                        v-for="(img, index) in signFilePath"
                        :key="'sign-' + index"
                      >
                        <van-image
                          width="100%"
                          height="120px"
                          fit="cover"
                          :src="img"
                          radius="4"
                          @click="previewImage(signFilePath, index)"
                        />
                      </van-swipe-item>
                    </van-swipe>
                    <div class="image-counter" v-if="signFilePath.length > 1">
                      {{ signActiveIndex + 1 }}/{{ signFilePath.length }}
                    </div>
                  </div>
                  <div class="location-image-empty" v-else>
                    <van-empty description="暂无图片" />
                  </div>
                </div>
              </template>
            </div>
          </div>

          <!-- 上报记录节点 -->
          <div
            class="timeline-item"
            v-if="reportRecords && reportRecords.length > 0"
          >
            <div class="timeline-dot active"></div>
            <div class="timeline-content">
              <div class="timeline-title">位置上报记录</div>
              <div
                class="timeline-info"
                v-for="(record, index) in reportRecords"
                :key="'report-' + index"
              >
                <div class="time-label">
                  上报时间 {{ record.reportTime }}
                  <span class="status-tag report-tag">已上报</span>
                </div>
                <div class="report-location" v-if="record.location">
                  位置信息: {{ record.location }}
                </div>
              </div>
            </div>
          </div>

          <!-- 下班打卡节点 -->
          <div class="timeline-item" v-if="clockInStatus === 'out'">
            <div
              class="timeline-dot"
              :class="{ active: clockInStatus === 'out' }"
            ></div>
            <div class="timeline-content">
              <div class="timeline-title">下班时间 {{ workEndTime }}</div>

              <template>
                <div class="timeline-info">
                  <div class="time-label">
                    打卡时间 {{ clockOutTime }}
                    <span
                      class="status-tag"
                      :class="{ 'early-tag': isEarlyLeave }"
                    >
                      {{ isEarlyLeave ? "早退" : "已打卡" }}
                    </span>
                  </div>
                  <div
                    class="location-image"
                    v-if="outFilePath && outFilePath.length > 0"
                  >
                    <van-swipe
                      :autoplay="3000"
                      indicator-color="#1989fa"
                      :show-indicators="outFilePath.length > 1"
                      @change="onOutSwipeChange"
                    >
                      <van-swipe-item
                        v-for="(img, index) in outFilePath"
                        :key="'out-' + index"
                      >
                        <van-image
                          width="100%"
                          height="120px"
                          fit="cover"
                          :src="img"
                          radius="4"
                          @click="previewImage(outFilePath, index)"
                        />
                      </van-swipe-item>
                    </van-swipe>
                    <div class="image-counter" v-if="outFilePath.length > 1">
                      {{ outActiveIndex + 1 }}/{{ outFilePath.length }}
                    </div>
                  </div>
                  <div class="location-image-empty" v-else>
                    <van-empty description="暂无图片" />
                  </div>
                </div>
              </template>
            </div>
          </div>
        </div>

        <!-- 打卡按钮 -->
        <div class="clock-btn-container">
          <template v-if="clockInStatus === 'none'">
            <div class="clock-in-btn" @click="showClockInPopup">
              <div class="btn-text">签到</div>
              <div class="btn-time">{{ currentTime }}</div>
            </div>
          </template>

          <template v-else-if="clockInStatus === 'in'">
            <div class="report-btns">
              <div class="small-btn report-btn" @click="handleReport">
                <div class="btn-text">上报</div>
              </div>
              <div class="small-btn clock-out-btn" @click="showClockOutPopup">
                <div class="btn-text">签退</div>
                <div class="btn-time">{{ currentTime }}</div>
              </div>
            </div>
          </template>
        </div>
      </template>
    </div>

    <!-- 打卡弹窗 -->
    <van-popup v-model="showPopup" round class="clock-popup">
      <div class="popup-title">{{ isClockIn ? "签到" : "签退" }}</div>

      <div class="popup-content">
        <div class="popup-time">
          打卡时间: {{ currentDate }} {{ currentTime }}
        </div>

        <div class="popup-location">
          <div class="location-label">请填写打卡备注 (选填)</div>
          <van-field
            v-model="clockRemark"
            placeholder="请输入"
            class="remark-input"
          />
        </div>

        <div class="upload-area">
          <vantFileUpload
            v-model="filStr"
            :file-type="['jpg', 'png']"
          ></vantFileUpload>
        </div>
      </div>

      <div class="popup-buttons">
        <van-button plain class="cancel-btn" @click="cancelClock"
          >暂不打卡</van-button
        >
        <van-button type="info" plain class="confirm-btn" @click="confirmClock">
          {{ isClockIn ? "打卡签到" : "打卡签退" }}
        </van-button>
      </div>
    </van-popup>
  </div>
</template>

<script>
import { formatDate, getDownLoadUrl } from "@/util";
import vantFileUpload from "@/components/vant-file-upload";
import { getClockTime, signOrOut, getClockInfo } from "@/api/clockIn";
import storeMixin from "@/store/mixin";

export default {
  name: "ClockInPage",
  mixins: [storeMixin],
  components: {
    vantFileUpload,
  },
  data() {
    return {
      avatar: require("@/assets/clockIn/avatar.png"),
      dateIcon: require("@/assets/clockIn/dateIcon.png"),
      currentDate: formatDate(new Date()),
      currentTime: "",
      clockInStatus: "none", // none, in, out  打卡状态: in:已经上班打卡 out:已经下班打卡 none:未打卡
      clockInTime: "", // 上班打卡时间
      clockOutTime: "", // 下班打卡时间
      showPopup: false,
      clockRemark: "",
      signFilePath: [], // 上班打卡图片
      outFilePath: [], // 下班打卡图片
      signActiveIndex: 0, // 上班打卡轮播图片当前索引
      outActiveIndex: 0, // 下班打卡轮播图片当前索引
      reportRecords: [], // 上报记录
      filStr: "", //打卡上传图片
      timer: null,
      isClockIn: true, // 是否是上班打卡 true:上班打卡 false:下班打卡
      workStartTime: "08:30:00", // 上班时间
      workEndTime: "17:30:00", // 下班时间
      loading: false,
      loadingInfo: false,
      loadError: false,
      locationError: false,
      location: {
        longitude: "",
        latitude: "",
      },
    };
  },
  computed: {
    // 判断是否迟到
    isLate() {
      return this.compareTime(this.clockInTime, this.workStartTime) > 0;
    },
    // 判断是否早退
    isEarlyLeave() {
      return this.compareTime(this.clockOutTime, this.workEndTime) < 0;
    },
  },
  created() {
    // 获取上下班规定时间
    this.getWorkTime();
    // 获取当前打卡状态
    this.getCurrentClockInfo();
  },
  mounted() {
    this.updateCurrentTime();
    this.timer = setInterval(this.updateCurrentTime, 1000);
    // 获取当前位置
    this.getLocation();
  },
  beforeDestroy() {
    if (this.timer) {
      clearInterval(this.timer);
    }
  },
  methods: {
    // 重试加载所有信息
    retryLoading() {
      this.loadError = false;
      this.getWorkTime();
      this.getCurrentClockInfo();
      this.getLocation();
    },

    // 获取上下班规定时间
    async getWorkTime() {
      try {
        const res = await getClockTime();
        if (res.code === 200 && res.data) {
          this.workStartTime = res.data.workStartTime || this.workStartTime;
          this.workEndTime = res.data.workEndTime || this.workEndTime;
          console.log(
            "工作时间获取成功:",
            this.workStartTime,
            this.workEndTime
          );
        } else {
          console.warn("工作时间获取失败，使用默认时间", res);
        }
      } catch (error) {
        console.error("获取工作时间异常:", error);
        // 使用默认值，不显示错误提示
      }
    },

    // 获取当天打卡信息
    async getCurrentClockInfo() {
      if (!this.user || !this.user.userId) {
        console.warn("用户信息未加载，无法获取打卡信息");
        this.loadError = true;
        return;
      }

      this.loadingInfo = true;
      this.loadError = false;

      // 重置图片相关状态
      this.signFilePath = [];
      this.outFilePath = [];
      this.signActiveIndex = 0;
      this.outActiveIndex = 0;
      this.reportRecords = [];

      try {
        const res = await getClockInfo({ userId: this.user.userId });

        if (res.code === 200) {
          // 设置打卡状态
          if (res.rows && res.rows.length > 0) {
            const clockData = res.rows[0];
            this.clockInTime = clockData.signTime || "";
            this.clockOutTime = clockData.outTime || "";
            this.clockInStatus =
              clockData.status == 1
                ? "in"
                : clockData.status == 2
                ? "out"
                : "none";

            // 设置图片内容
            if (clockData.signFilePath) {
              this.signFilePath = this.getImageArr(clockData.signFilePath);
              console.log("上班打卡图片:", this.signFilePath);
            }

            if (clockData.outFilePath) {
              this.outFilePath = this.getImageArr(clockData.outFilePath);
              console.log("下班打卡图片:", this.outFilePath);
            }

            // 设置上报记录
            if (
              clockData.collectorsAttendanceInfoVOList &&
              clockData.collectorsAttendanceInfoVOList.length > 0
            ) {
              this.reportRecords = clockData.collectorsAttendanceInfoVOList.map(
                (record) => ({
                  reportTime: record.time || "",
                  location: `经度: ${Number(record.longitude).toFixed(
                    6
                  )}, 纬度: ${Number(record.latitude).toFixed(6)}`,
                })
              );
              console.log("上报记录:", this.reportRecords);
            }
          } else {
            this.clockInStatus = "none";
          }

          console.log("打卡信息获取成功:", res);
        } else {
          console.warn("没有获取到打卡信息或返回错误:", res);
          // 使用默认值（未打卡状态）
        }
      } catch (error) {
        console.error("获取打卡信息异常:", error);
        this.loadError = true;
      } finally {
        this.loadingInfo = false;
      }
    },

    //图片处理
    getImageArr(str) {
      if (!str) return [];
      try {
        const arr = str.split(",").filter((item) => item && item.trim());
        return arr.map((item) => getDownLoadUrl(item.trim()));
      } catch (error) {
        console.error("处理图片地址出错:", error);
        return [];
      }
    },

    // 获取位置信息  谷歌浏览器需要域名为https才能正确请求位置信息
    getLocation() {
      if (this.isApp) {
        this.locationError = false;
        this.$toast.loading({
          message: "获取位置中...",
          duration: 1000,
        });
        window.parent.postMessage(
          {
            cmd: "getPosition",
            options: {
              type: "gcj02",
            },
          },
          "*"
        );
        const watchPosition = (event) => {
          if (event.data.cmd == "getPosition") {
            if (event.data.value && event.data.value.longitude) {
              let res = event.data.value;
              // this.$toast(res.longitude + "," + res.latitude);
              this.location.longitude = res.longitude;
              this.location.latitude = res.latitude;
              console.log("获取位置成功:", this.location);
              // this.loadError=false
              this.$toast.clear();
              setTimeout(() => {
                window.removeEventListener("message", watchPosition);
              }, 2000);
            }
          }
        };
        window.addEventListener("message", watchPosition);
      } else {
        this.locationError = false;
        if (navigator.geolocation) {
          this.$toast.loading({
            message: "获取位置中...",
            duration: 1000,
          });

          navigator.geolocation.getCurrentPosition(
            (position) => {
              this.location.longitude = position.coords.longitude;
              this.location.latitude = position.coords.latitude;
              console.log("获取位置成功:", this.location);
              this.$toast.clear();
            },
            (error) => {
              console.error("获取位置失败:", error);
              this.locationError = true;
              this.$toast.clear();
              this.$toast.fail("获取位置失败，请确保已开启定位权限");
            },
            {
              enableHighAccuracy: true,
              timeout: 10000,
              maximumAge: 0,
            }
          );
        } else {
          this.locationError = true;
          this.$toast.fail("您的浏览器不支持获取地理位置");
        }
      }
    },
    updateCurrentTime() {
      const now = new Date();
      const hours = String(now.getHours()).padStart(2, "0");
      const minutes = String(now.getMinutes()).padStart(2, "0");
      const seconds = String(now.getSeconds()).padStart(2, "0");
      this.currentTime = `${hours}:${minutes}:${seconds}`;
    },

    onClickLeft() {
      this.$router.go(-1);
    },

    showClockInPopup() {
      // 检查是否已经上班打卡
      if (this.clockInStatus !== "none") {
        this.$toast("今日已完成签到");
        return;
      }

      // 检查位置信息
      if (!this.location.longitude || !this.location.latitude) {
        this.$dialog
          .confirm({
            title: "位置信息未获取",
            message: "需要获取您的位置信息才能打卡，是否重新获取？",
            confirmButtonText: "重新获取",
            cancelButtonText: "取消",
          })
          .then(() => {
            this.getLocation();
          })
          .catch(() => {
            // 用户取消
          });
        return;
      }

      // 清空表单内容
      this.clockRemark = "";
      this.filStr = "";

      this.isClockIn = true;
      this.showPopup = true;
    },

    showClockOutPopup() {
      // 检查是否已经下班打卡
      if (this.clockInStatus === "out") {
        this.$toast("今日已完成签退");
        return;
      }

      // 检查是否先签到
      if (this.clockInStatus === "none") {
        this.$toast("请先进行签到");
        return;
      }

      // 检查位置信息
      if (!this.location.longitude || !this.location.latitude) {
        this.$dialog
          .confirm({
            title: "位置信息未获取",
            message: "需要获取您的位置信息才能打卡，是否重新获取？",
            confirmButtonText: "重新获取",
            cancelButtonText: "取消",
          })
          .then(() => {
            this.getLocation();
          })
          .catch(() => {
            // 用户取消
          });
        return;
      }

      // 清空表单内容
      this.clockRemark = "";
      this.filStr = "";

      this.isClockIn = false;
      this.showPopup = true;
    },

    cancelClock() {
      this.showPopup = false;
      this.clockRemark = "";
      this.filStr = "";
    },

    async confirmClock() {
      if (this.loading) return;

      if (!this.location.longitude || !this.location.latitude) {
        this.$toast("位置信息获取失败，请重试");
        this.getLocation();
        return;
      }

      // 检查是否上传了图片
      if (!this.filStr || this.filStr.length === 0) {
        this.$toast("请上传打卡照片");
        return;
      }

      this.loading = true;
      const status = this.isClockIn ? 1 : 2; // 1为签到，2为签退

      try {
        this.$toast.loading({
          message: this.isClockIn ? "签到中..." : "签退中...",
          forbidClick: true,
        });

        // 获取并处理文件路径，用于临时显示
        const tempFilePaths = this.getImageArr(this.filStr);

        const clockData = {
          nowLongitude: this.location.longitude,
          nowLatitude: this.location.latitude,
          status: status,
          remark: this.clockRemark,
          filePath: this.filStr,
        };

        console.log("打卡数据:", clockData);

        const res = await signOrOut(clockData);

        if (res.code === 200) {
          if (this.isClockIn) {
            this.clockInTime = this.currentTime;
            this.clockInStatus = "in";

            // 更新签到图片预览
            if (tempFilePaths.length > 0) {
              this.signFilePath = tempFilePaths;
            }

            // 判断是否迟到并提示
            if (this.compareTime(this.currentTime, this.workStartTime) > 0) {
              this.$toast("您已迟到，签到成功但请注意考勤");
            } else {
              this.$toast.success("签到成功");
            }
          } else {
            this.clockOutTime = this.currentTime;
            this.clockInStatus = "out";

            // 更新签退图片预览
            if (tempFilePaths.length > 0) {
              this.outFilePath = tempFilePaths;
            }

            // 判断是否早退并提示
            if (this.compareTime(this.currentTime, this.workEndTime) < 0) {
              this.$toast("您已早退，签退成功但请注意考勤");
            } else {
              this.$toast.success("签退成功");
            }
          }

          // 重新获取打卡信息以更新界面
          setTimeout(() => {
            this.getCurrentClockInfo();
          }, 1000);
        } else {
          this.$toast.fail(res.msg || "打卡失败");
        }
      } catch (error) {
        console.error("打卡异常:", error);
        this.$toast.fail("打卡失败，请稍后重试");
      } finally {
        this.loading = false;
        this.$toast.clear();
        this.showPopup = false;
        this.clockRemark = "";
        this.filStr = "";
      }
    },

    // 比较时间大小
    compareTime(time1, time2) {
      // 转换为秒数进行比较
      const getSeconds = (timeStr) => {
        const [hours, minutes, seconds] = timeStr.split(":").map(Number);
        return hours * 3600 + minutes * 60 + (seconds || 0);
      };

      return getSeconds(time1) - getSeconds(time2);
    },

    previewImage(imageArray, index) {
      if (!imageArray || imageArray.length === 0) return;
      this.$imagePreview({
        images: imageArray,
        startPosition: index,
        closeable: true,
        closeIconPosition: "top-right",
      });
    },

    // 上班打卡图片轮播切换事件
    onSignSwipeChange(index) {
      this.signActiveIndex = index;
    },

    // 下班打卡图片轮播切换事件
    onOutSwipeChange(index) {
      this.outActiveIndex = index;
    },

    handleReport() {
      // 检查位置信息
      if (!this.location.longitude || !this.location.latitude) {
        this.$dialog
          .confirm({
            title: "位置信息未获取",
            message: "需要获取您的位置信息才能上报，是否重新获取？",
            confirmButtonText: "重新获取",
            cancelButtonText: "取消",
          })
          .then(() => {
            this.getLocation();
          })
          .catch(() => {
            // 用户取消
          });
        return;
      }

      this.$dialog
        .confirm({
          title: "确认上报",
          message: "确定要上报当前位置吗？",
          confirmButtonText: "确认上报",
          cancelButtonText: "取消",
        })
        .then(async () => {
          try {
            this.$toast.loading({
              message: "上报中...",
              forbidClick: true,
            });

            const reportData = {
              nowLongitude: this.location.longitude,
              nowLatitude: this.location.latitude,
              status: 3, // 3代表上报
            };

            console.log("上报数据:", reportData);

            const res = await signOrOut(reportData);

            if (res.code === 200) {
              this.$toast.success("上报成功");
              setTimeout(() => {
                this.getCurrentClockInfo();
              }, 1000);
            } else {
              this.$toast.fail(res.msg || "上报失败");
            }
          } catch (error) {
            console.error("上报异常:", error);
            this.$toast.fail("上报失败，请稍后重试");
          } finally {
            this.$toast.clear();
          }
        })
        .catch(() => {
          // 用户取消
        });
    },
  },
};
</script>

<style lang="scss" scoped>
.clock-in-page {
  min-height: 100vh;
  background-color: #f5f7fa;
  padding-top: 46px;
}

.head-bg {
  height: 61px;
  background: url("~@/assets/clockIn/banner01.png") no-repeat;
  background-size: 100% 100%;
}

.user-card {
  margin: -40px 15px 15px 15px;
  background-color: #ffffff;
  border-radius: 8px;
  padding: 15px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  color: white;
  box-shadow: 0 2px 12px rgba(0, 0, 0, 0.05);

  .user-info {
    display: flex;
    align-items: center;

    .avatar {
      margin-right: 10px;
    }

    .info {
      .name {
        color: #374151;
        font-size: 16px;
        font-weight: 500;
        margin-bottom: 4px;
      }

      .date {
        color: #6b7280;
        font-size: 14px;
        opacity: 0.8;

        .dept-name {
          display: inline-block;
          color: #1989fa;
          font-size: 12px;
          border-radius: 10px;
        }
      }
    }
  }
}

.clock-status {
  margin: 20px 15px;
  background-color: white;
  border-radius: 8px;
  padding: 20px;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.05);

  // 时间轴样式
  .timeline {
    position: relative;
    padding-left: 20px;

    // 时间轴线
    .timeline-line {
      position: absolute;
      left: 5px;
      top: 10px;
      bottom: 10px;
      width: 1px;
      background-color: #e8e8e8;
    }

    // 时间轴节点
    .timeline-item {
      position: relative;
      margin-bottom: 30px;

      &:last-child {
        margin-bottom: 0;
      }

      // 时间轴圆点
      .timeline-dot {
        position: absolute;
        left: -21px;
        top: 3px;
        width: 13px;
        height: 13px;
        border-radius: 50%;
        background-color: #e8e8e8;
        border: 2px solid #fff;
        z-index: 2;

        &.active {
          background-color: #1989fa;
        }
      }

      // 时间轴内容
      .timeline-content {
        padding-bottom: 15px;

        .timeline-title {
          font-size: 15px;
          font-weight: 500;
          color: #333;
          margin-bottom: 10px;
        }

        .timeline-info {
          background-color: #f9f9f9;
          border-radius: 8px;
          padding: 12px;
          margin-top: 8px;

          .time-label {
            font-size: 14px;
            color: #333;
            margin-bottom: 10px;
            display: flex;
            align-items: center;

            .status-tag {
              display: inline-block;
              background-color: #f2f8ff;
              color: #1989fa;
              font-size: 12px;
              padding: 2px 6px;
              border-radius: 4px;
              margin-left: 8px;

              &.late-tag {
                background-color: #fff2f0;
                color: #ff4d4f;
              }

              &.early-tag {
                background-color: #fff7e6;
                color: #fa8c16;
              }

              &.report-tag {
                background-color: rgba(255, 152, 0, 0.1);
                color: #ff9800;
              }
            }
          }

          .location-image {
            width: 100%;
            height: 120px;
            border-radius: 4px;
            overflow: hidden;
            position: relative;

            .van-swipe {
              height: 100%;
            }

            .image-counter {
              position: absolute;
              right: 8px;
              bottom: 8px;
              background-color: rgba(0, 0, 0, 0.5);
              color: white;
              font-size: 12px;
              padding: 2px 8px;
              border-radius: 10px;
              z-index: 2;
            }
          }

          .location-image-empty {
            width: 100%;
            height: 120px;
            display: flex;
            align-items: center;
            justify-content: center;
            background-color: #f5f7fa;
            border-radius: 4px;
          }

          .report-location {
            font-size: 13px;
            color: #666;
            margin-top: 5px;
          }
        }
      }
    }
  }

  // 打卡按钮容器
  .clock-btn-container {
    display: flex;
    justify-content: center;
    margin-top: 20px;
  }

  // 打卡按钮
  .clock-in-btn {
    width: 120px;
    height: 120px;
    border-radius: 50%;
    background-color: #1989fa;
    display: flex;
    flex-direction: column;
    justify-content: center;
    align-items: center;
    color: white;
    box-shadow: 0 4px 16px rgba(25, 137, 250, 0.3);

    .btn-text {
      font-size: 20px;
      font-weight: 500;
      margin-bottom: 5px;
    }

    .btn-time {
      font-size: 14px;
      opacity: 0.8;
    }
  }

  // 上报和打卡按钮容器
  .report-btns {
    display: flex;
    justify-content: center;
    align-items: center;
    gap: 20px;

    .small-btn {
      width: 100px;
      height: 100px;
      border-radius: 50%;
      display: flex;
      flex-direction: column;
      justify-content: center;
      align-items: center;
      color: white;
      box-shadow: 0 4px 12px rgba(0, 0, 0, 0.2);
      transition: transform 0.2s ease;

      &:active {
        transform: scale(0.95);
      }

      .btn-text {
        font-size: 18px;
        font-weight: 500;
        margin-bottom: 5px;
      }

      .btn-time {
        font-size: 12px;
        opacity: 0.8;
      }
    }

    .report-btn {
      background-color: #ff9800;
      box-shadow: 0 4px 12px rgba(255, 152, 0, 0.3);
    }

    .clock-out-btn {
      background-color: #1989fa;
      box-shadow: 0 4px 12px rgba(25, 137, 250, 0.3);
    }
  }

  // 加载和错误状态
  .loading-container,
  .error-container {
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    min-height: 200px;

    .loading-text,
    .error-text {
      margin-top: 15px;
      font-size: 14px;
      color: #909399;
    }

    .van-button {
      margin-top: 10px;
    }
  }
}

.clock-popup {
  width: 80%;
  border-radius: 12px;
  overflow: hidden;

  .popup-title {
    text-align: center;
    font-size: 18px;
    font-weight: 500;
    padding: 15px 0;
    border-bottom: 1px solid #eee;
  }

  .popup-content {
    padding: 15px;

    .popup-time {
      width: 100%;
      height: 41px;
      line-height: 41px;
      text-align: center;
      background: #dfebfb;
      font-size: 14px;
      color: #428ffc;
      margin-bottom: 15px;
      border-radius: 5px;
    }

    .popup-location {
      margin-bottom: 20px;

      .location-label {
        font-size: 14px;
        color: #333;
        margin-bottom: 8px;
      }

      .remark-input {
        border-radius: 4px;
        background-color: #f5f7fa;
      }
    }

    .upload-area {
      display: flex;
      justify-content: space-around;
      margin-top: 20px;

      .upload-item {
        display: flex;
        flex-direction: column;
        align-items: center;

        .upload-icon {
          color: #1989fa;
          margin-bottom: 5px;
        }

        .upload-text {
          font-size: 12px;
          color: #666;
        }
      }
    }
  }

  .popup-buttons {
    display: flex;
    border-top: 1px solid #eee;

    .van-button {
      flex: 1;
      border: none;
      border-radius: 0;
      height: 44px;
      font-size: 16px;
    }

    .cancel-btn {
      color: #666;
    }
  }
}

// 添加一些动画效果
.clock-in-btn {
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.95);
  }
}

.upload-item {
  transition: transform 0.2s ease;

  &:active {
    transform: scale(0.9);
  }
}

// 添加时间轴动画
.timeline-dot {
  transition: background-color 0.3s ease;
}

.timeline-info {
  animation: fadeIn 0.5s ease;
}

@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}
</style>
